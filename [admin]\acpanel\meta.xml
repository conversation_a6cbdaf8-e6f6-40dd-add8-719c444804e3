<meta>
	<info description="Anti-Cheat Control Panel" author="ccw" version="0.1.8" type="script" />
	<min_mta_version server="1.3.1" client="1.3.1"></min_mta_version>

	<script src="_common.lua"/>
	<script src="s_joiner.lua"/>
	<script src="s_main.lua"/>
	<script src="s_settings.lua"/>
	<script src="s_img_mod.lua"/>

	<script src="_common.lua" type="client" />
	<script src="c_joiner.lua" type="client" />
	<script src="c_main.lua" type="client" />
	<script src="c_settings.lua" type="client"/>
	<script src="c_gui.lua" type="client" />
	<script src="c_gui_ac.lua" type="client" />
	<script src="c_gui_ac2.lua" type="client" />
	<script src="c_gui_block_mods.lua" type="client" />
	<script src="c_gui_config.lua" type="client" />
	<script src="c_model_switcher.lua" type="client" />


	<settings>
		<setting name="*admingroup" value="Admin,AdminPlus"
					friendlyname="Admin group list"
					group="_Advanced"
					accept="*"
					examples="Admin,Moderator,SuperModerator"
					desc="To use this resource, the player must belong to one of the groups listed."
					/>
		<setting name="*switchplayermodels" value="false"
					friendlyname="Switch player model if modified"
					group="_Advanced"
					accept="true,false"
					examples="true,false"
					desc="Rather than kick a player for modified player model, switch them to an unmodified model on their client only, so the server and other players will still see the normal skin. Bear in mind that if your scripts use client side skin checks this could affect them. If you enable this you won't need to have 'Player Models' listed in block Img mods.."
					/>
	</settings>

	<!--
		Allow these rights by typing the following into the server or client console:
		aclrequest allow acpanel all
	-->
	<aclrequest>
        <right name="function.setServerConfigSetting" access="true"></right>
        <right name="function.kickPlayer" access="true"></right>
        <right name="function.fetchRemote" access="true"></right>
	</aclrequest>
</meta>
