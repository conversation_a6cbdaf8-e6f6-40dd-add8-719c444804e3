<messages>
  <!-- Accepted subgroups:
            all - Output to chatbox for all
            log - Log file output
            player - Victim/Player chatbox output
            admin - Admin chatbox output
       Variables:
            $player - player
            $admin - admin
            $data - Additional information, not all the nodes use it.
            $data2 - Additional information, not all the nodes use it.
    -->
  <player>
    <group action="ban" r="255" g="0" b="0">
      <all>$player has been banned$by_admin_4all. $data2</all>
      <log>ADMIN: $admin has banned $player $data $data2</log>
    </group>
    <group action="kick" r="255" g="0" b="0">
      <all>$player has been kicked$by_admin_4all.</all>
      <log>ADMIN: $admin has kicked $player $data</log>
    </group>
    <group action="mute" r="255" g="0" b="0">
      <all>$player has been muted$by_admin_4all. $data2</all>
      <log>ADMIN: $admin has muted $player $data $data2</log>
    </group>
	<group action="setnick" r="255" g="0" b="0">
      <log>ADMIN: $player name changed to $data2 $by_admin_4all.</log>
    </group>
    <group action="unmute" r="0" g="255" b="100">
      <all>$player has been unmuted$by_admin_4all.</all>
      <log>ADMIN: $admin has unmuted $player</log>
    </group>
    <group action="freeze" r="255" g="0" b="0">
      <admin>$player has been frozen$by_admin_4all.</admin>
      <player>You have been frozen$by_admin_4plr.</player>
      <log>ADMIN: $admin has frozen $player</log>
    </group>
    <group action="unfreeze" r="0" g="255" b="100">
      <admin>$player has been unfrozen$by_admin_4all.</admin>
      <player>You have been unfrozen$by_admin_4plr.</player>
      <log>ADMIN: $admin has unfrozen $player</log>
    </group>
    <group action="slap" r="235" g="20" b="200">
      <all>$player has been slapped$by_admin_4all. ($data HP)</all>
      <log>ADMIN: $admin has slapped $player ($data HP)</log>
    </group>
    <group action="shout" r="255" g="100" b="30">
      <admin>You have shouted at $player</admin>
    </group>
    <group action="sethealth" r="0" g="170" b="170">
      <admin>$player's health has been changed to $data</admin>
      <player>Your health has been changed to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed $player's health to $data</log>
    </group>
    <group action="setarmour" r="122" g="122" b="122">
      <admin>$player's armour has been changed to $data</admin>
      <player>Your armour has been changed to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed $player's armour to $data</log>
    </group>
    <group action="setmoney" r="225" g="170" b="90">
      <admin>$player's money has been set to $data</admin>
      <player>Your money has been set to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has set $player's money to $data</log>
    </group>
    <group action="setskin" r="255" g="122" b="0">
      <admin>$player's skin has been changed to $data</admin>
      <player>Your skin has been changed to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed $player's skin to $data</log>
    </group>
    <group action="setteam" r="255" g="255" b="0">
      <admin>$player has been moved to the $data team.</admin>
      <player>You have been moved to the $data team$by_admin_4plr.</player>
      <log>ADMIN: $admin has moved $player to the $data team</log>
    </group>
	<group action="removefromteam" r="255" g="255" b="0">
      <admin>$player has been removed from the $data team.</admin>
      <player>You have been removed from the $data team$by_admin_4plr.</player>
      <log>ADMIN: $admin has removed $player from the $data team</log>
    </group>
    <group action="setinterior" r="0" g="255" b="0">
      <admin>$player has been moved to the $data interior.</admin>
      <player>You have been moved to the $data interior$by_admin_4plr.</player>
      <log>ADMIN: $admin has moved $player to the $data interior</log>
    </group>
    <group action="setdimension" r="0" g="255" b="0">
      <admin>$player has been moved to the $data dimension.</admin>
      <player>You have been moved to the $data dimension$by_admin_4plr.</player>
      <log>ADMIN: $admin has moved $player to the $data dimension</log>
    </group>
    <group action="setstat" r="255" g="122" b="0">
      <admin>$player's $data has been set to $data2</admin>
      <player>Your $data has been set to $data2$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed $player's $data to $data2</log>
    </group>
    <group action="jetpackr" r="255" g="0" b="0">
      <admin>$player's jetpack has been removed</admin>
      <player>Your jetpack has been removed$by_admin_4plr.</player>
      <log>ADMIN: $admin has removed $player's jetpack</log>
    </group>
    <group action="jetpacka" r="255" g="122" b="0">
      <admin>$player has been given a jetpack</admin>
      <player>You have been given a jetpack$by_admin_4plr.</player>
      <log>ADMIN: $admin has given a jetpack to $player</log>
    </group>
    <group action="adminr" r="255" g="0" b="0">
      <admin>$player's admin rights have been revoked</admin>
      <player>Your admin rights have been revoked$by_admin_4plr.</player>
      <log>ADMIN: $admin has revoked admin privilegies from $player</log>
    </group>
    <group action="admina" r="0" g="255" b="0">
      <admin>$player has been given admin rights</admin>
      <player>You have been give admin rights$by_admin_4plr.</player>
      <log>ADMIN: $admin has given admin privilegies to $player</log>
    </group>
    <group action="givevehicle" r="0" g="255" b="0">
      <admin>$player has been given a '$data'</admin>
      <player>You have been given a '$data'$by_admin_4plr.</player>
      <log>ADMIN: $admin has given $player a '$data'" </log>
    </group>
    <group action="giveweapon" r="0" g="0" b="255">
      <admin>$player has been given a '$data'($data2)</admin>
      <player>You have been given a '$data'($data2)$by_admin_4plr.</player>
      <log>ADMIN: $admin has given $player '$data'</log>
    </group>
    <group action="warp" r="0" g="200" b="50">
      <admin>You have warped to $player</admin>
      <log>ADMIN: $admin has warped to $player</log>
    </group>
    <group action="warpto" r="0" g="200" b="50">
      <admin>$player has been warped to $data</admin>
      <player>You have been warped to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has moved $player to $data</log>
    </group>
  </player>
  <team>
    <group action="createteam" r="225" g="170" b="90">
      <admin>Successfully created team '$data'</admin>
      <log>ADMIN: $admin has created a team '$data'</log>
    </group>
    <group action="destroyteam" r="225" g="170" b="90">
      <admin>Successfully destroyed team '$data'</admin>
      <log>ADMIN: $admin has destroyed a team '$data'</log>
    </group>
  </team>
  <vehicle>
    <group action="repair" r="0" g="255" b="0">
      <admin>$player's vehicle has been fixed</admin>
      <player>Your vehicle has been fixed$by_admin_4plr.</player>
      <log>ADMIN: $admin has fixed vehicle of $player</log>
    </group>
    <group action="blowvehicle" r="255" g="0" b="0">
      <admin>$player's vehicle has been blown</admin>
      <player>Your vehicle has been blown$by_admin_4plr.</player>
      <log>ADMIN: $admin has blown vehicle of $player</log>
    </group>
    <group action="destroyvehicle" r="255" g="0" b="0">
      <admin>$player's vehicle has been destroyed</admin>
      <player>Your vehicle has been destroyed$by_admin_4plr.</player>
      <log>ADMIN: $admin has destroyed vehicle of $player</log>
    </group>
    <group action="customize" r="225" g="170" b="90">
      <admin>$player's vehicle has been upgraded. ($data)</admin>
      <player>Your vehicle has been upgraded$by_admin_4plr.</player>
      <log>ADMIN: $admin has upgraded vehicle of $player ($data)</log>
    </group>
    <group action="customizer" r="225" g="170" b="90">
      <admin>$player's vehicle upgrades have been removed.</admin>
      <player>Your vehicle upgrades have been removed$by_admin_4plr.</player>
      <log>ADMIN: $admin has has removed vehicle upgrades of $player</log>
    </group>
    <group action="setpaintjob" r="225" g="170" b="90">
      <admin>$player's vehicle paint job set to $data</admin>
      <player>Your vehicle paint job has been changed to $data$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed paint job of $player's vehicle to $data</log>
    </group>
    <group action="setcolor" r="225" g="170" b="90">
      <admin>$player's vehicle color changed</admin>
      <player>Your vehicle color has been changed$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed color of $player's vehicle</log>
    </group>
	<group action="setlights" r="225" g="170" b="90">
      <admin>$player's vehicle lights color changed</admin>
      <player>Your vehicle lights color has been changed$by_admin_4plr.</player>
      <log>ADMIN: $admin has changed lights color of $player's vehicle</log>
    </group>
	<group action="setplates" r="225" g="170" b="90">
      <admin>$player's license plates have been changed to '$data'</admin>
      <player>Your vehicle license plates have been changed to '$data'$by_admin_4plr</player>
      <log>ADMIN: $admin has changed license plates of $player's vehicle to '$data'</log>
    </group>
  </vehicle>
  <resource>

  </resource>
  <server>
    <group action="setgame" r="225" g="170" b="90">
      <admin>ASE gamemode info has been changed to '$data'</admin>
      <log>ADMIN: $admin has changed ASE gamemode to '$data'</log>
    </group>
    <group action="setmap" r="225" g="170" b="90">
      <admin>ASE map info has been changed to '$data'</admin>
      <log>ADMIN: $admin has changed ASE map info to '$data'</log>
    </group>
    <group action="setwelcome" r="225" g="170" b="90">
      <admin>Welcome message has been set to '$data'</admin>
      <log>ADMIN: $admin has set welcome message to '$data'</log>
    </group>
    <group action="resetwelcome" r="225" g="170" b="90">
      <admin>Welcome message has been reset</admin>
      <log>ADMIN: $admin has reset the welcome message</log>
    </group>
    <group action="setpassword" r="225" g="170" b="90">
      <admin>Server password set to '$data'</admin>
      <log>ADMIN: $admin has set server password to '$data'</log>
    </group>
    <group action="resetpassword" r="225" g="170" b="90">
      <admin>Server password has been reset</admin>
      <log>ADMIN: $admin has reset server password</log>
    </group>
    <group action="settime" r="225" g="170" b="90">
      <all>Time set to $data$by_admin_4all.</all>
      <log>ADMIN: $admin has changed time to $data</log>
    </group>
    <group action="setweather" r="225" g="170" b="90">
      <all>Weather set to '$data'$by_admin_4all.</all>
      <log>ADMIN: $admin has set weather to '$data'</log>
    </group>
    <group action="blendweather" r="225" g="170" b="90">
      <admin>Blending weather to '$data'</admin>
      <log>ADMIN: $admin is blending weather to '$data'</log>
    </group>
    <group action="setgravity" r="225" g="170" b="90">
      <all>Gravitation value set to '$data'$by_admin_4all.</all>
      <log>ADMIN: $admin has set gravitation to $data</log>
    </group>
    <group action="setgamespeed" r="225" g="170" b="90">
      <all>Game speed set to '$data'$by_admin_4all.</all>
      <log>ADMIN: $admin has set game speed to '$data'</log>
    </group>
    <group action="setwaveheight" r="225" g="170" b="90">
      <all>Waves height set to '$data'$by_admin_4all.</all>
      <log>ADMIN: $admin has set waves height to '$data'</log>
    </group>
    <group action="setblurlevel" r="225" g="170" b="90">
      <all>Blur level set to '$data'$by_admin_4all.</all>
      <log>ADMIN: $admin has set blur level to '$data'</log>
    </group>
  </server>
  <bans>

  </bans>
  <admin>
    <group action="create" r="225" g="170" b="90">
      <admin>$data has been successfully created</admin>
      <log>ADMIN: $data has been created by $admin</log>
    </group>
    <group action="destroy" r="225" g="170" b="90">
      <admin>$data has been successfully destroyed</admin>
      <log>ADMIN: $data has been destroyed by $admin</log>
    </group>
    <group action="password" r="50" g="255" b="50">
      <admin>You password has been successfully changed to '$data'</admin>
    </group>
    <group action="settings" r="225" g="170" b="90">
      <admin>Set '$data' to '$data2'</admin>
      <log>ADMIN: Set '$data' to '$data2' by $admin</log>
    </group>
  </admin>
</messages>
