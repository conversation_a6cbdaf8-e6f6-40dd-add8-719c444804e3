<meta>
	<info author="jbeta" name="Scripting console" type="script" version="1.0.1" showInResourceBrowser="true" />
	<oop>true</oop>

	<include resource="ajax" />
	
	<script src="client.lua" type="client"/>
	<script src="client_util.lua" type="client"/>
	<script src="server.lua" type="server"/>
	<script src="server_util.lua" type="server"/>

	<html src="runcode.htm" default="true"/>
	<html src="runcode.css" raw="true"/>
	<html src="runcode.js" raw="true"/>
	
	<!-- codemirror files http://codemirror.net/ -->
	<html src="codemirror\codemirror.js" raw="true"/>
	<html src="codemirror\codemirror.css" raw="true"/>
	<html src="codemirror\docs.css" raw="true"/>
	<html src="codemirror\neat.css" raw="true"/>
	<html src="codemirror\lua.js" raw="true"/>

	<export function="httpRun" http="true"/>
</meta>