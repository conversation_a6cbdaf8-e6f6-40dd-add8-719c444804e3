local allowedArenas = {
	["dd"] = true,
	["race"] = true,
	["race training"] = true,
}

function updateTooltipPosition(_,_, x, y)
	guiSetPosition(label, x + 8, y + 10, false)
	guiBringToFront(label)
end


function saveSettingsToFile()
	local xml = xmlCreateFile("nos_settings.xml", "settings")
	if not xml then return false end

	local styleNode = xmlCreateChild(xml, "behavior")
	xmlNodeSetAttribute(styleNode, "sustain", tostring(g_Settings.SustainOnPickup))
	xmlNodeSetAttribute(styleNode, "control", g_Settings.ControlStyle or "normal")
	
	local ret = xmlSaveFile(xml)
	xmlUnloadFile(xml)
	return ret
end


function loadSettingsFromFile()
	local xml = xmlLoadFile("nos_settings.xml")
	if not xml then return false end
	
	local styleNode = xmlFindChild(xml, "behavior", 0)
	g_Settings.SustainOnPickup = (xmlNodeGetAttribute(styleNode, "sustain") == "true")
	g_Settings.ControlStyle = xmlNodeGetAttribute(styleNode, "control") or "normal"
			
	xmlUnloadFile(xml)
	return true
end

-- Command to set the client's NOS control style
function consoleSetNosFiringStyle(commandName, nosControl)
	if not isEnabled() then
		return
	end
	local settingName = nosControl
	if nosControl ~= "hybrid" and nosControl ~= "nfs" and nosControl ~= "normal" then
		if commandName == "nos" then
			outputChatBox("#19846dSyntax :: #ffffff/nos [normal / nfs / hybrid]", 255, 255, 255, true)
		else
		end
		return
	end
	if g_Settings.ControlStyle ~= nosControl then
		g_Settings.ControlStyle = nosControl
		outputChatBox("#19846dNos Mode :: #ffffffNitro is now set to #19846d"..nosControl:upper(), 255, 255, 255, true)
		if saveSettingsToFile() then
			--alert("Nos stilin '" .. tostring(settingName) .. "' olarak degistirildi.")
		end
	end
end
addCommandHandler("nos", consoleSetNosFiringStyle)

function isEnabled(arenaID)
	if not arenaID then
		arenaID = getElementData(getElementParent(localPlayer), "id")
	end
	return allowedArenas[arenaID]
end